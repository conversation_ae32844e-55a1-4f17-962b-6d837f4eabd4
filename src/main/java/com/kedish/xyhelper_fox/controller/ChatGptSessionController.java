package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.ChatGptSessionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/chatGpt/session")
public class ChatGptSessionController {

    @Resource
    private ChatGptSessionService chatGptSessionService;
    @PostMapping("/list")
    public FoxPageResult backEndList(@RequestBody PageQueryReq req) {

        return FoxPageResult.fromPage(chatGptSessionService.page(req));
    }

    @PostMapping("/addSession")
    public FoxResult addSession(@RequestBody AddGptSessionReq req) {
        log.info("addSession req:{}", JSON.toJSONString(req));
        return FoxResult.ok(chatGptSessionService.addChatGptSession(req));
    }

    @PostMapping("/updateSession")
    public FoxResult updateSession(@RequestBody AddGptSessionReq req) {
        log.info("updateSession req:{}", JSON.toJSONString(req));
        return FoxResult.ok(chatGptSessionService.updateChatGptSession(req));
    }

    @PostMapping("/deleteSession")
    public FoxResult deleteSession(@RequestBody List<Long> ids) {
        log.info("deleteSession id:{}", JSON.toJSONString(ids));
        return FoxResult.ok(chatGptSessionService.deleteChatGptSession(ids));
    }

    @GetMapping("/generateCarId")
    public FoxResult generateCarId() {
        return FoxResult.ok(chatGptSessionService.generateCarId());
    }
}
